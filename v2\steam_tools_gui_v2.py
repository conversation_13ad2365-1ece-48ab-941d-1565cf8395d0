import sys
import os
import json
import time
import threading
import hashlib
import winreg
import requests
import traceback
import platform
import uuid
import psutil
import ctypes
from datetime import datetime
from pathlib import Path

from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                           QWidget, QLabel, QPushButton, QLineEdit, QTextEdit, 
                           QProgressBar, QFrame, QScrollArea, QGridLayout,
                           QMessageBox, QFileDialog, QInputDialog, QSplashScreen)
from PyQt6.QtCore import Qt, QTimer, QThread, pyqtSignal, QPropertyAnimation, QEasingCurve, QRect, QSize, QPoint
from PyQt6.QtGui import QFont, QPixmap, QPainter, QColor, QLinearGradient, QPen, QBrush, QIcon, QPalette

# PyQt-Fluent-Widgets will be imported after QApplication is created
FLUENT_WIDGETS_AVAILABLE = False
FluentIcon = PushButton = PrimaryPushButton = None
CardWidget = BodyLabel = CaptionLabel = SubtitleLabel = None
LineEdit = ProgressBar = TransparentPushButton = None
Theme = setTheme = isDarkTheme = qconfig = None
ElevatedCardWidget = SimpleCardWidget = HeaderCardWidget = None
setThemeColor = setCustomStyleSheet = None

def init_fluent_widgets():
    """Initialize PyQt-Fluent-Widgets after QApplication is created"""
    global FLUENT_WIDGETS_AVAILABLE
    # For now, let's disable fluent widgets to test the basic functionality
    FLUENT_WIDGETS_AVAILABLE = False
    print("PyQt-Fluent-Widgets disabled for testing")
    return False

# Add parent directory to path to import keyauth
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))
try:
    from keyauth import api
except ImportError:
    print("Warning: keyauth module not found")
    api = None

class SteamTheme:
    """Steam-inspired color theme with modern design"""
    # Steam primary colors as hex strings
    BACKGROUND = "#0e1419"
    SURFACE = "#1b2838"
    SURFACE_VARIANT = "#171a21"
    PRIMARY = "#66c0f4"
    PRIMARY_VARIANT = "#4c9fff"
    SECONDARY = "#2a475e"
    SUCCESS = "#4c6b22"
    WARNING = "#ffb800"
    ERROR = "#cd412b"

    # Text colors
    ON_BACKGROUND = "#c7d5e0"
    ON_SURFACE = "#c7d5e0"
    ON_PRIMARY = "#0e1419"
    MUTED = "#8b98a5"

    @classmethod
    def get_qcolor(cls, color_name):
        """Get QColor object for a color name"""
        color_hex = getattr(cls, color_name)
        return QColor(color_hex)

class ConnectionStatusWidget(QWidget):
    """Modern connection status indicator positioned like HTML design"""

    def __init__(self):
        super().__init__()
        self.status = "connecting"
        self.init_ui()
        self.setup_animations()

    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)

        # Connection dot indicator
        self.connection_dot = QLabel("●")
        self.connection_dot.setFixedSize(12, 12)
        self.connection_dot.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.connection_dot.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.SUCCESS};
                font-size: 12px;
            }}
        """)

        # Status text layout
        text_layout = QVBoxLayout()
        text_layout.setSpacing(2)

        self.status_label = QLabel("Connected to License Server")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE};
                font-weight: 500;
                font-size: 12px;
            }}
        """)

        self.details_label = QLabel("auth.steamtools.com • 24ms")
        self.details_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 10px;
            }}
        """)

        text_layout.addWidget(self.status_label)
        text_layout.addWidget(self.details_label)

        layout.addWidget(self.connection_dot)
        layout.addLayout(text_layout)

        # Modern card styling
        self.setStyleSheet(f"""
            ConnectionStatusWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {SteamTheme.SURFACE},
                    stop:1 {SteamTheme.SURFACE_VARIANT});
                border: 1px solid {SteamTheme.SECONDARY};
                border-radius: 6px;
            }}
        """)

    def setup_animations(self):
        self.pulse_timer = QTimer()
        self.pulse_timer.timeout.connect(self.pulse_animation)
        self.pulse_timer.start(2000)

    def pulse_animation(self):
        if self.status == "connecting":
            # Animate connecting dot
            current_color = self.connection_dot.styleSheet()
            if SteamTheme.PRIMARY in current_color:
                color = SteamTheme.WARNING
            else:
                color = SteamTheme.PRIMARY
            self.connection_dot.setStyleSheet(f"""
                QLabel {{
                    color: {color};
                    font-size: 12px;
                }}
            """)

    def update_status(self, status, message=None, details=None):
        self.status = status

        status_config = {
            "connecting": {
                "color": SteamTheme.WARNING,
                "message": "Connecting to License Server"
            },
            "connected": {
                "color": SteamTheme.SUCCESS,
                "message": "Connected to License Server"
            },
            "disconnected": {
                "color": SteamTheme.ERROR,
                "message": "Disconnected from Server"
            },
            "error": {
                "color": SteamTheme.ERROR,
                "message": "Connection Error"
            }
        }

        config = status_config.get(status, status_config["connecting"])

        self.connection_dot.setStyleSheet(f"""
            QLabel {{
                color: {config["color"]};
                font-size: 12px;
            }}
        """)

        self.status_label.setText(message or config["message"])
        if details:
            self.details_label.setText(details)

class ServerStatusCard(QWidget):
    """Server status panel with metrics like HTML design"""

    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_animations()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 12, 16, 12)
        layout.setSpacing(12)

        # Header with server icon and status
        header_layout = QHBoxLayout()
        header_layout.setSpacing(8)

        server_icon = QLabel("🖥️")
        server_icon.setFixedSize(16, 16)

        title_label = QLabel("Authentication Server")
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE};
                font-size: 13px;
                font-weight: 600;
            }}
        """)

        self.server_status_dot = QLabel("●")
        self.server_status_dot.setFixedSize(10, 10)
        self.server_status_dot.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.SUCCESS};
                font-size: 10px;
            }}
        """)

        header_layout.addWidget(server_icon)
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.server_status_dot)

        # Metrics grid
        metrics_layout = QHBoxLayout()
        metrics_layout.setSpacing(24)

        # Latency
        latency_layout = QVBoxLayout()
        latency_layout.setSpacing(4)
        latency_label = QLabel("LATENCY")
        latency_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 10px;
                font-weight: 500;
                letter-spacing: 1px;
            }}
        """)
        self.latency_value = QLabel("24ms")
        self.latency_value.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.SUCCESS};
                font-family: 'Consolas', monospace;
                font-size: 13px;
                font-weight: 600;
            }}
        """)
        latency_layout.addWidget(latency_label)
        latency_layout.addWidget(self.latency_value)

        # Uptime
        uptime_layout = QVBoxLayout()
        uptime_layout.setSpacing(4)
        uptime_label = QLabel("UPTIME")
        uptime_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 10px;
                font-weight: 500;
                letter-spacing: 1px;
            }}
        """)
        self.uptime_value = QLabel("99.9%")
        self.uptime_value.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.SUCCESS};
                font-family: 'Consolas', monospace;
                font-size: 13px;
                font-weight: 600;
            }}
        """)
        uptime_layout.addWidget(uptime_label)
        uptime_layout.addWidget(self.uptime_value)

        metrics_layout.addLayout(latency_layout)
        metrics_layout.addLayout(uptime_layout)
        metrics_layout.addStretch()

        # Data flow indicator
        self.data_flow = QWidget()
        self.data_flow.setFixedHeight(2)
        self.data_flow.setStyleSheet(f"""
            QWidget {{
                background-color: {SteamTheme.SURFACE_VARIANT};
                border-radius: 1px;
            }}
        """)

        layout.addLayout(header_layout)
        layout.addLayout(metrics_layout)
        layout.addWidget(self.data_flow)

        # Card styling
        self.setStyleSheet(f"""
            ServerStatusCard {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {SteamTheme.SECONDARY},
                    stop:1 {SteamTheme.SURFACE});
                border: 1px solid {SteamTheme.SECONDARY};
                border-radius: 6px;
            }}
        """)

    def setup_animations(self):
        # Data flow animation
        self.flow_timer = QTimer()
        self.flow_timer.timeout.connect(self.animate_data_flow)
        self.flow_timer.start(3000)

        # Update metrics
        self.metrics_timer = QTimer()
        self.metrics_timer.timeout.connect(self.update_metrics)
        self.metrics_timer.start(5000)

    def animate_data_flow(self):
        current_style = self.data_flow.styleSheet()
        if SteamTheme.PRIMARY in current_style:
            color = SteamTheme.SURFACE_VARIANT
        else:
            color = SteamTheme.PRIMARY

        self.data_flow.setStyleSheet(f"""
            QWidget {{
                background-color: {color};
                border-radius: 1px;
            }}
        """)

    def update_metrics(self):
        import random
        latencies = [18, 24, 31, 19, 27, 22, 29]
        uptimes = ['99.9%', '99.8%', '100%', '99.7%']

        latency = random.choice(latencies)
        uptime = random.choice(uptimes)

        self.latency_value.setText(f"{latency}ms")
        self.uptime_value.setText(uptime)

class ProgressWidget(QWidget):
    """Modern progress widget with enhanced styling"""

    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setSpacing(12)

        # Progress header
        header_layout = QHBoxLayout()

        if FLUENT_WIDGETS_AVAILABLE:
            self.progress_label = BodyLabel("Activation Progress")
        else:
            self.progress_label = QLabel("Activation Progress")
            self.progress_label.setStyleSheet(f"""
                QLabel {{
                    color: {SteamTheme.ON_SURFACE};
                    font-size: 14px;
                    font-weight: 500;
                }}
            """)

        self.percentage_label = QLabel("0%")
        self.percentage_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE};
                font-family: 'Consolas', monospace;
                font-size: 14px;
                font-weight: 600;
            }}
        """)

        header_layout.addWidget(self.progress_label)
        header_layout.addStretch()
        header_layout.addWidget(self.percentage_label)

        # Progress bar with enhanced styling
        if FLUENT_WIDGETS_AVAILABLE:
            self.progress_bar = ProgressBar()
        else:
            self.progress_bar = QProgressBar()

        self.progress_bar.setFixedHeight(8)
        self.progress_bar.setTextVisible(False)

        if not FLUENT_WIDGETS_AVAILABLE:
            self.progress_bar.setStyleSheet(f"""
                QProgressBar {{
                    border: none;
                    border-radius: 4px;
                    background-color: {SteamTheme.SURFACE_VARIANT};
                }}
                QProgressBar::chunk {{
                    border-radius: 4px;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {SteamTheme.PRIMARY},
                        stop:1 {SteamTheme.PRIMARY_VARIANT});
                }}
            """)

        layout.addLayout(header_layout)
        layout.addWidget(self.progress_bar)

    def update_progress(self, value, message=None):
        self.progress_bar.setValue(value)
        self.percentage_label.setText(f"{value}%")

        # Update parent's status indicators if available
        try:
            parent_widget = self.parent()
            while parent_widget and not hasattr(parent_widget, 'status_dot'):
                parent_widget = parent_widget.parent()

            if parent_widget and hasattr(parent_widget, 'status_dot'):
                if value == 0:
                    color = SteamTheme.MUTED
                    status = "Ready"
                elif value < 100:
                    color = SteamTheme.PRIMARY
                    status = "Processing..."
                else:
                    color = SteamTheme.SUCCESS
                    status = "Complete"

                parent_widget.status_dot.setStyleSheet(f"""
                    QLabel {{
                        color: {color};
                        font-size: 8px;
                    }}
                """)
                parent_widget.status_text.setText(status)
        except:
            # Ignore errors during initialization
            pass



class LicenseActivationThread(QThread):
    """Background thread for license activation"""
    
    progress_updated = pyqtSignal(int, str)
    status_message = pyqtSignal(str)
    activation_complete = pyqtSignal(bool, str)
    
    def __init__(self, license_key, keyauth_app, steam_path, app_info):
        super().__init__()
        self.license_key = license_key
        self.keyauth_app = keyauth_app
        self.steam_path = steam_path
        self.app_info = app_info
        
    def run(self):
        try:
            self.progress_updated.emit(10, "Validating license key...")
            time.sleep(0.5)
            
            # Validate license
            if not self.keyauth_app:
                self.activation_complete.emit(False, "Keyauth not initialized")
                return
                
            is_valid = self.keyauth_app.license(self.license_key)
            if not is_valid:
                self.activation_complete.emit(False, "Invalid license key")
                return
                
            self.progress_updated.emit(30, "License validated successfully")
            self.status_message.emit("✅ License key validated")
            time.sleep(0.5)
            
            self.progress_updated.emit(50, "Processing activation files...")
            time.sleep(1)
            
            # Simulate file processing
            success = self.process_activation_files()
            
            if success:
                self.progress_updated.emit(100, "Activation completed successfully!")
                self.activation_complete.emit(True, "Steam activation completed successfully!")
            else:
                self.activation_complete.emit(False, "Failed to process activation files")
                
        except Exception as e:
            self.activation_complete.emit(False, f"Activation error: {str(e)}")
            
    def process_activation_files(self):
        try:
            # Create necessary directories
            stplug_path = os.path.join(self.steam_path, "config", "stplug-in")
            os.makedirs(stplug_path, exist_ok=True)
            
            # Simulate file downloads with progress updates
            files = ["HID Library", "Lua Packer", "Steam Tools", "Game Script"]
            
            for i, file_name in enumerate(files):
                progress = 50 + (i + 1) * 10
                self.progress_updated.emit(progress, f"Processing {file_name}...")
                self.status_message.emit(f"📥 Processing {file_name}...")
                time.sleep(0.8)  # Simulate processing time
                
            return True
            
        except Exception as e:
            self.status_message.emit(f"❌ Error processing files: {str(e)}")
            return False

class ModernSteamToolsGUI(QMainWindow):
    """Modern Steam Tools GUI with PyQt-Fluent-Widgets styling"""
    
    def __init__(self):
        super().__init__()
        self.keyauth_app = None
        self.steam_path = ""
        self.license_key = ""
        self.hwid = self.generate_hwid()
        
        # Setup theme and styling
        self.setup_theme()
        self.init_ui()
        self.init_keyauth()
        self.auto_detect_steam_path()
        
    def setup_theme(self):
        """Setup Steam-inspired dark theme"""
        if FLUENT_WIDGETS_AVAILABLE:
            setTheme(Theme.DARK)
            # Use hex string for setThemeColor
            setThemeColor(SteamTheme.PRIMARY)

        # Apply custom dark palette
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {SteamTheme.BACKGROUND};
                color: {SteamTheme.ON_BACKGROUND};
            }}
        """)
        
    def init_ui(self):
        """Initialize the modern user interface"""
        self.setWindowTitle("🎮 Steam Tools - License Activation")
        self.setFixedSize(800, 750)
        self.setWindowIcon(self.create_app_icon())

        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout - full screen container
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(0)
        main_layout.setContentsMargins(0, 0, 0, 0)

        # Create overlay for connection status (top-right positioning)
        self.create_connection_overlay(central_widget)

        # Content layout with proper spacing
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(24)
        content_layout.setContentsMargins(32, 32, 32, 32)
        content_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Header
        self.create_header(content_layout)

        # Main card
        self.create_main_card(content_layout)

        main_layout.addWidget(content_widget)

        # Status bar
        self.create_status_bar()

    def create_connection_overlay(self, parent):
        """Create connection status overlay positioned in top-right"""
        self.connection_widget = ConnectionStatusWidget()
        self.connection_widget.setParent(parent)
        self.connection_widget.setFixedSize(280, 60)

        # Position in top-right corner
        def position_connection_widget():
            parent_rect = parent.rect()
            widget_rect = self.connection_widget.rect()
            x = parent_rect.width() - widget_rect.width() - 20
            y = 20
            self.connection_widget.move(x, y)

        # Position initially and on resize
        QTimer.singleShot(100, position_connection_widget)

        # Handle resize events
        original_resize = parent.resizeEvent if hasattr(parent, 'resizeEvent') else lambda e: None
        def new_resize_event(event):
            original_resize(event)
            position_connection_widget()
        parent.resizeEvent = new_resize_event
        
    def create_app_icon(self):
        """Create application icon"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(Qt.GlobalColor.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)
        
        # Draw Steam-like icon
        gradient = QLinearGradient(0, 0, 32, 32)
        gradient.setColorAt(0, QColor(SteamTheme.PRIMARY))
        gradient.setColorAt(1, QColor(SteamTheme.PRIMARY_VARIANT))

        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.PenStyle.NoPen)
        painter.drawRoundedRect(0, 0, 32, 32, 8, 8)

        painter.setPen(QPen(QColor(SteamTheme.ON_PRIMARY), 2))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎮")
        painter.end()
        
        return QIcon(pixmap)
        
    def create_header(self, layout):
        """Create header section"""
        header_widget = QWidget()
        header_layout = QVBoxLayout(header_widget)
        header_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        header_layout.setSpacing(8)
        
        # Title
        title_label = QLabel("🎮 STEAM TOOLS")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.PRIMARY};
                font-size: 32px;
                font-weight: bold;
                padding: 0px;
            }}
        """)
        
        # Subtitle
        subtitle_label = QLabel("License Activation")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 16px;
                padding: 0px;
            }}
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        layout.addWidget(header_widget)
        
    def create_main_card(self, layout):
        """Create main activation card with modern styling"""
        # Use PyQt-Fluent-Widgets card if available
        if FLUENT_WIDGETS_AVAILABLE:
            card_widget = ElevatedCardWidget()
        else:
            card_widget = QWidget()

        card_widget.setMaximumWidth(500)

        # Apply styling
        if not FLUENT_WIDGETS_AVAILABLE:
            card_widget.setStyleSheet(f"""
                QWidget {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {SteamTheme.SURFACE},
                        stop:1 {SteamTheme.SURFACE_VARIANT});
                    border: 1px solid {SteamTheme.SECONDARY};
                    border-radius: 12px;
                }}
            """)

        card_layout = QVBoxLayout(card_widget)
        card_layout.setSpacing(24)
        card_layout.setContentsMargins(32, 32, 32, 32)

        # Server status panel (like HTML design)
        server_status = ServerStatusCard()
        card_layout.addWidget(server_status)

        # Logo section
        self.create_logo_section(card_layout)

        # License input section
        self.create_license_section(card_layout)

        # Activate button
        self.create_activate_button(card_layout)

        # Progress section
        self.progress_widget = ProgressWidget()
        card_layout.addWidget(self.progress_widget)

        # HWID section
        self.create_hwid_section(card_layout)

        # Support buttons
        self.create_support_buttons(card_layout)

        layout.addWidget(card_widget, 0, Qt.AlignmentFlag.AlignCenter)
        
    def create_logo_section(self, layout):
        """Create logo section"""
        logo_widget = QWidget()
        logo_widget.setStyleSheet(f"""
            QWidget {{
                background-color: {SteamTheme.SURFACE_VARIANT};
                border: 1px solid {SteamTheme.SECONDARY};
                border-radius: 8px;
            }}
        """)
        
        logo_layout = QVBoxLayout(logo_widget)
        logo_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        logo_layout.setSpacing(12)
        logo_layout.setContentsMargins(20, 20, 20, 20)
        
        # Icon
        icon_label = QLabel("🎮")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px;")
        
        # Text
        text_label = QLabel("Steam Tools")
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.PRIMARY};
                font-size: 24px;
                font-weight: bold;
            }}
        """)
        
        logo_layout.addWidget(icon_label)
        logo_layout.addWidget(text_label)
        layout.addWidget(logo_widget)
        
    def create_license_section(self, layout):
        """Create license input section with modern styling"""
        # Label
        if FLUENT_WIDGETS_AVAILABLE:
            license_label = BodyLabel("License Key")
        else:
            license_label = QLabel("License Key")
            license_label.setStyleSheet(f"""
                QLabel {{
                    color: {SteamTheme.ON_SURFACE};
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 8px;
                }}
            """)

        # Input with PyQt-Fluent-Widgets styling
        if FLUENT_WIDGETS_AVAILABLE:
            self.license_input = LineEdit()
            self.license_input.setPlaceholderText("XXXX-XXXX-XXXX-XXXX")
        else:
            self.license_input = QLineEdit()
            self.license_input.setPlaceholderText("XXXX-XXXX-XXXX-XXXX")

        self.license_input.setFixedHeight(48)
        self.license_input.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # Apply custom styling for better appearance
        if not FLUENT_WIDGETS_AVAILABLE:
            self.license_input.setStyleSheet(f"""
                QLineEdit {{
                    background-color: {SteamTheme.SURFACE_VARIANT};
                    border: 2px solid {SteamTheme.SECONDARY};
                    border-radius: 8px;
                    padding: 0px 16px;
                    color: {SteamTheme.ON_SURFACE};
                    font-size: 18px;
                    font-family: 'Consolas', monospace;
                    letter-spacing: 2px;
                }}
                QLineEdit:focus {{
                    border-color: {SteamTheme.PRIMARY};
                    background-color: {SteamTheme.SURFACE};
                }}
            """)

        # Bind Enter key
        self.license_input.returnPressed.connect(self.activate_license)

        layout.addWidget(license_label)
        layout.addWidget(self.license_input)

    def create_activate_button(self, layout):
        """Create activate button using PyQt-Fluent-Widgets if available"""
        if FLUENT_WIDGETS_AVAILABLE:
            self.activate_button = PrimaryPushButton("🚀 Activate License")
        else:
            self.activate_button = QPushButton("🚀 Activate License")

        self.activate_button.setFixedHeight(56)
        self.activate_button.setCursor(Qt.CursorShape.PointingHandCursor)
        self.activate_button.clicked.connect(self.activate_license)

        # Apply custom styling for better appearance if not using Fluent Widgets
        if not FLUENT_WIDGETS_AVAILABLE:
            self.activate_button.setStyleSheet(f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {SteamTheme.PRIMARY},
                        stop:1 {SteamTheme.PRIMARY_VARIANT});
                    border: none;
                    border-radius: 8px;
                    color: {SteamTheme.ON_PRIMARY};
                    font-size: 18px;
                    font-weight: 600;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 {SteamTheme.PRIMARY_VARIANT},
                        stop:1 {SteamTheme.PRIMARY});
                }}
                QPushButton:pressed {{
                    background-color: {SteamTheme.SECONDARY};
                }}
                QPushButton:disabled {{
                    background-color: {SteamTheme.SECONDARY};
                    color: {SteamTheme.MUTED};
                }}
            """)

        layout.addWidget(self.activate_button)

    def create_hwid_section(self, layout):
        """Create HWID section like HTML design"""
        hwid_widget = QWidget()
        hwid_widget.setStyleSheet(f"""
            QWidget {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 {SteamTheme.SECONDARY},
                    stop:1 {SteamTheme.SURFACE});
                border: 1px solid {SteamTheme.SECONDARY};
                border-radius: 8px;
            }}
        """)

        hwid_layout = QVBoxLayout(hwid_widget)
        hwid_layout.setContentsMargins(20, 16, 20, 16)
        hwid_layout.setSpacing(12)

        # HWID label
        hwid_label = QLabel("Hardware ID")
        hwid_label.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 13px;
                font-weight: 500;
            }}
        """)

        # HWID value (clickable)
        self.hwid_value = QPushButton(self.hwid)
        self.hwid_value.setCursor(Qt.CursorShape.PointingHandCursor)
        self.hwid_value.clicked.connect(self.copy_hwid)
        self.hwid_value.setStyleSheet(f"""
            QPushButton {{
                background-color: {SteamTheme.SURFACE_VARIANT};
                border: none;
                border-radius: 6px;
                padding: 12px;
                color: {SteamTheme.ON_SURFACE};
                font-family: 'Consolas', monospace;
                font-size: 14px;
                text-align: center;
            }}
            QPushButton:hover {{
                background-color: {SteamTheme.SURFACE};
            }}
        """)

        # Status indicator
        status_layout = QHBoxLayout()
        status_layout.setSpacing(8)

        self.status_dot = QLabel("●")
        self.status_dot.setFixedSize(8, 8)
        self.status_dot.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_dot.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.MUTED};
                font-size: 8px;
            }}
        """)

        self.status_text = QLabel("Ready")
        self.status_text.setStyleSheet(f"""
            QLabel {{
                color: {SteamTheme.ON_SURFACE};
                font-size: 13px;
            }}
        """)

        status_layout.addWidget(self.status_dot)
        status_layout.addWidget(self.status_text)
        status_layout.addStretch()

        hwid_layout.addWidget(hwid_label)
        hwid_layout.addWidget(self.hwid_value)
        hwid_layout.addLayout(status_layout)

        layout.addWidget(hwid_widget)

    def copy_hwid(self):
        """Copy HWID to clipboard"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.hwid)

        # Show feedback
        original_text = self.hwid_value.text()
        self.hwid_value.setText("✅ Copied!")
        QTimer.singleShot(2000, lambda: self.hwid_value.setText(original_text))
        
    def create_support_buttons(self, layout):
        """Create support buttons with modern styling"""
        support_layout = QHBoxLayout()
        support_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        support_layout.setSpacing(16)

        # Help button
        if FLUENT_WIDGETS_AVAILABLE:
            help_btn = TransparentPushButton("❓ Help")
            support_btn = TransparentPushButton("💬 Support")
        else:
            help_btn = QPushButton("❓ Help")
            support_btn = QPushButton("💬 Support")

        for btn in [help_btn, support_btn]:
            btn.setFixedSize(120, 40)
            btn.setCursor(Qt.CursorShape.PointingHandCursor)

            if not FLUENT_WIDGETS_AVAILABLE:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: transparent;
                        border: 1px solid {SteamTheme.SECONDARY};
                        border-radius: 6px;
                        color: {SteamTheme.MUTED};
                        font-size: 13px;
                        font-weight: 500;
                    }}
                    QPushButton:hover {{
                        border-color: {SteamTheme.PRIMARY};
                        color: {SteamTheme.PRIMARY};
                    }}
                """)

        help_btn.clicked.connect(self.show_help)
        support_btn.clicked.connect(self.show_support)

        support_layout.addWidget(help_btn)
        support_layout.addWidget(support_btn)

        layout.addLayout(support_layout)
        
    def create_status_bar(self):
        """Create status bar"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet(f"""
            QStatusBar {{
                background-color: {SteamTheme.SURFACE};
                border-top: 1px solid {SteamTheme.SECONDARY};
                color: {SteamTheme.ON_SURFACE};
                font-size: 11px;
                padding: 8px;
            }}
        """)
        
        # Admin status
        admin_status = "🛡️ Admin: Yes" if self.is_admin() else "⚠️ Admin: No"
        steam_status = f"🎮 Steam: {self.steam_path}" if self.steam_path else "❌ Steam: Not Found"
        
        status_bar.showMessage(f"{admin_status} • {steam_status}")
        
    def generate_hwid(self):
        """Generate Hardware ID"""
        try:
            system_info = []
            
            # MAC Address
            try:
                import uuid
                mac = uuid.getnode()
                system_info.append(f"MAC:{mac}")
            except:
                pass
                
            # System info
            try:
                system_info.append(f"SYSTEM:{platform.system()}")
                system_info.append(f"MACHINE:{platform.machine()}")
                system_info.append(f"NODE:{platform.node()}")
            except:
                pass
                
            if system_info:
                combined_info = "|".join(sorted(system_info))
                hwid_hash = hashlib.sha256(combined_info.encode()).hexdigest()
                return "-".join([hwid_hash[i:i+4].upper() for i in range(0, 16, 4)])
            else:
                return str(uuid.uuid4()).replace("-", "")[:16].upper()
                
        except Exception:
            return "HWID-" + str(uuid.uuid4()).replace("-", "")[:12].upper()
            
    def is_admin(self):
        """Check if running as administrator"""
        try:
            return ctypes.windll.shell32.IsUserAnAdmin()
        except:
            return False
            
    def auto_detect_steam_path(self):
        """Auto-detect Steam installation path"""
        try:
            # Try registry lookup
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Valve\Steam") as key:
                steam_path = winreg.QueryValueEx(key, "InstallPath")[0]
                if os.path.exists(steam_path):
                    self.steam_path = steam_path
                    print(f"Steam path detected: {steam_path}")
                    return
        except:
            pass
            
        # Try common paths
        common_paths = [
            r"C:\Program Files (x86)\Steam",
            r"C:\Program Files\Steam",
            r"D:\Steam",
            r"E:\Steam"
        ]
        
        for path in common_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "steam.exe")):
                self.steam_path = path
                print(f"Steam path found: {path}")
                return

        print("Steam installation not found automatically")
        
    def init_keyauth(self):
        """Initialize Keyauth API"""
        def init_in_background():
            try:
                self.connection_widget.update_status("connecting", "Connecting to license server...", "Initializing Keyauth API...")
                
                checksum = self.get_checksum()
                self.keyauth_app = api(
                    name="MainSteam",
                    ownerid="1tGVnUKtzH",
                    secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
                    version="1.0",
                    hash_to_check=checksum
                )
                
                # Update UI on main thread
                QTimer.singleShot(100, lambda: self.connection_widget.update_status(
                    "connected", 
                    "Connected to license server",
                    "Connection established successfully"
                ))
                print("✅ Keyauth initialized successfully")
                
            except Exception as e:
                QTimer.singleShot(100, lambda: self.connection_widget.update_status(
                    "error",
                    "License server error", 
                    "Connection failed"
                ))
                print("❌ Failed to initialize connection")
                self.keyauth_app = None
                
        threading.Thread(target=init_in_background, daemon=True).start()
        
    def get_checksum(self):
        """Get file checksum for Keyauth"""
        try:
            md5_hash = hashlib.md5()
            with open(__file__, "rb") as f:
                md5_hash.update(f.read())
            return md5_hash.hexdigest()
        except:
            return ""
            
    def activate_license(self):
        """Activate license key"""
        license_key = self.license_input.text().strip()
        
        if not license_key:
            QMessageBox.warning(self, "Error", "Please enter a license key")
            return
            
        if not self.keyauth_app:
            QMessageBox.warning(self, "Error", "Not connected to license server. Please wait and try again.")
            return
            
        if not self.steam_path:
            self.auto_detect_steam_path()
            if not self.steam_path:
                QMessageBox.critical(self, "Error", "Steam installation not found. Please contact administrator.")
                return
                
        # Check if Steam is running
        if self.is_steam_running():
            reply = QMessageBox.question(
                self, 
                "Steam Running", 
                "Steam is currently running and must be closed before activation. Close Steam now?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                self.close_steam_processes()
            else:
                return
                
        # Start activation in background thread
        self.activate_button.setEnabled(False)
        self.activate_button.setText("⏳ Activating...")
        self.license_input.setEnabled(False)
        
        # Get app info (simplified for this example)
        app_info = {"app_id": "123456", "app_name": "Steam Game"}
        
        self.activation_thread = LicenseActivationThread(
            license_key, self.keyauth_app, self.steam_path, app_info
        )
        self.activation_thread.progress_updated.connect(self.progress_widget.update_progress)
        self.activation_thread.status_message.connect(lambda msg: print(msg))
        self.activation_thread.activation_complete.connect(self.on_activation_complete)
        self.activation_thread.start()
        
    def on_activation_complete(self, success, message):
        """Handle activation completion"""
        self.activate_button.setEnabled(True)
        self.license_input.setEnabled(True)
        
        if success:
            self.activate_button.setText("✅ Activated")
            self.activate_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SteamTheme.SUCCESS};
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }}
            """)
            QMessageBox.information(self, "Success", message)
        else:
            self.activate_button.setText("❌ Failed")
            self.activate_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {SteamTheme.ERROR};
                    border: none;
                    border-radius: 8px;
                    color: white;
                    font-size: 16px;
                    font-weight: bold;
                }}
            """)
            QMessageBox.critical(self, "Error", message)
            
        # Reset button after delay
        QTimer.singleShot(3000, self.reset_activate_button)
        
    def reset_activate_button(self):
        """Reset activate button to original state"""
        self.activate_button.setText("🚀 Activate License")
        self.activate_button.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY}, 
                    stop:1 {SteamTheme.PRIMARY_VARIANT});
                border: none;
                border-radius: 8px;
                color: {SteamTheme.ON_PRIMARY};
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 {SteamTheme.PRIMARY_VARIANT}, 
                    stop:1 {SteamTheme.PRIMARY});
            }}
        """)
        
    def is_steam_running(self):
        """Check if Steam is running"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    return True
            return False
        except Exception:
            return False
            
    def close_steam_processes(self):
        """Close Steam processes"""
        try:
            steam_processes = []
            for proc in psutil.process_iter(['pid', 'name']):
                if proc.info['name'] and proc.info['name'].lower() in ['steam.exe', 'steamwebhelper.exe', 'steamservice.exe']:
                    steam_processes.append(proc)
                    
            for proc in steam_processes:
                try:
                    proc.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
            time.sleep(2)
            
            for proc in steam_processes:
                try:
                    if proc.is_running():
                        proc.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
                    
            print(f"✅ Closed {len(steam_processes)} Steam process(es)")

        except Exception as e:
            print(f"❌ Error closing Steam: {str(e)}")
            
    def show_help(self):
        """Show help dialog"""
        QMessageBox.information(
            self,
            "Help",
            "Steam Tools License Activation Help:\n\n"
            "1. Enter your license key\n"
            "2. Click 'Activate License' to begin\n"
            "3. Wait for the activation process to complete\n\n"
            "For support, contact your administrator."
        )
        
    def show_support(self):
        """Show support dialog"""
        QMessageBox.information(
            self,
            "Support",
            "Need help? Contact support:\n\n"
            "Please contact your administrator for assistance.\n"
            "Include any error messages when requesting help."
        )

def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Steam Tools")
    app.setApplicationVersion("2.0")

    # Initialize PyQt-Fluent-Widgets after QApplication is created
    init_fluent_widgets()
    
    # Create and show splash screen
    splash_pixmap = QPixmap(400, 200)
    splash_pixmap.fill(QColor(SteamTheme.BACKGROUND))
    
    painter = QPainter(splash_pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # Draw splash content
    painter.setPen(QPen(QColor(SteamTheme.PRIMARY), 2))
    painter.setFont(QFont("Arial", 24, QFont.Weight.Bold))
    painter.drawText(splash_pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎮 Steam Tools v2.0\nLoading...")
    painter.end()
    
    splash = QSplashScreen(splash_pixmap)
    splash.show()
    app.processEvents()
    
    # Create main window
    window = ModernSteamToolsGUI()
    
    # Show main window and hide splash
    QTimer.singleShot(2000, lambda: (splash.finish(window), window.show()))
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()